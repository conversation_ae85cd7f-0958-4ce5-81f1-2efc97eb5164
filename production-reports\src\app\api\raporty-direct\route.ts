import { NextRequest, NextResponse } from "next/server";
const Database = require("better-sqlite3");
import path from "path";

export async function GET() {
  let db: any = null;

  try {
    const dbPath = path.join(process.cwd(), "baza_danych.db");
    db = new Database(dbPath);

    const raporty = db
      .prepare(
        `
      SELECT 
        r.*,
        p.kod_handlowy as produkt_kod,
        p.nazwa as produkt_nazwa,
        m.nazwa as maszyna_nazwa,
        pr.imie as pracownik_imie,
        pr.nazwisko as pracownik_nazwisko,
        s.nazwa as surowiec_nazwa,
        mat.nazwa as material_nazwa
      FROM raporty_produkcyjne r
      LEFT JOIN produkty p ON r.produkt_id = p.id
      LEFT JOIN maszyny m ON r.maszyna_id = m.id
      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id
      LEFT JOIN surowce s ON r.surowiec_id = s.id
      LEFT JOIN materialy mat ON r.material_id = mat.id
      ORDER BY r.data_utworzenia DESC
    `
      )
      .all();

    return NextResponse.json(raporty);
  } catch (error) {
    console.error("Błąd podczas pobierania raportów:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania raportów" },
      { status: 500 }
    );
  } finally {
    if (db) {
      db.close();
    }
  }
}

export async function POST(request: NextRequest) {
  let db: any = null;

  try {
    const data = await request.json();
    console.log("Received data for report creation:", data);

    const dbPath = path.join(process.cwd(), "baza_danych.db");
    db = new Database(dbPath);

    // The frontend sends arrays for surowce and materialy.
    // The database expects a single entry. We'll take the first one from each array.
    const surowiec =
      data.surowce && data.surowce.length > 0 ? data.surowce[0] : {};
    const material =
      data.materialy && data.materialy.length > 0 ? data.materialy[0] : {};

    // Insert the report
    const insertReport = db.prepare(`
      INSERT INTO raporty_produkcyjne (
        data_utworzenia,
        produkt_id,
        metraz_rolek,
        ilosc_rolek,
        surowiec_id,
        zuzyty_surowiec,
        odpad_surowiec,
        material_id,
        zuzyty_material,
        odpad_material,
        maszyna_id,
        pracownik_id,
        czas_pracy_maszyny,
        czas_pracy_pracownika,
        uwagi
      ) VALUES (
        datetime('now'),
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
      )
    `);

    const result = insertReport.run(
      data.produkt_id,
      data.metraz_rolek,
      data.ilosc_rolek,
      surowiec.surowiec_id || null,
      surowiec.zuzyty_surowiec || null,
      surowiec.odpad_surowiec || null,
      material.material_id || null,
      material.zuzyty_material || null,
      material.odpad_material || null,
      data.maszyna_id,
      data.pracownik_id,
      data.czas_pracy_maszyny,
      data.czas_pracy_pracownika,
      data.uwagi || null
    );

    console.log("Report inserted with ID:", result.lastInsertRowid);

    // Fetch the created report with related data
    const createdReport = db
      .prepare(
        `
      SELECT
        r.*,
        p.kod_handlowy as produkt_kod,
        p.nazwa as produkt_nazwa,
        m.nazwa as maszyna_nazwa,
        pr.imie as pracownik_imie,
        pr.nazwisko as pracownik_nazwisko,
        s.nazwa as surowiec_nazwa,
        mat.nazwa as material_nazwa
      FROM raporty_produkcyjne r
      LEFT JOIN produkty p ON r.produkt_id = p.id
      LEFT JOIN maszyny m ON r.maszyna_id = m.id
      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id
      LEFT JOIN surowce s ON r.surowiec_id = s.id
      LEFT JOIN materialy mat ON r.material_id = mat.id
      WHERE r.id = ?
    `
      )
      .get(result.lastInsertRowid);

    return NextResponse.json(createdReport, { status: 201 });
  } catch (error) {
    console.error("Błąd podczas tworzenia raportu:", error);
    return NextResponse.json(
      {
        error: "Błąd podczas tworzenia raportu",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  } finally {
    if (db) {
      db.close();
    }
  }
}
