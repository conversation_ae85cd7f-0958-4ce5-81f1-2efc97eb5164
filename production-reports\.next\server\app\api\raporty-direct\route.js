/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/raporty-direct/route";
exports.ids = ["app/api/raporty-direct/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fraporty-direct%2Froute&page=%2Fapi%2Fraporty-direct%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fraporty-direct%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fraporty-direct%2Froute&page=%2Fapi%2Fraporty-direct%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fraporty-direct%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_raporty_direct_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/raporty-direct/route.ts */ \"(rsc)/./src/app/api/raporty-direct/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/raporty-direct/route\",\n        pathname: \"/api/raporty-direct\",\n        filename: \"route\",\n        bundlePath: \"app/api/raporty-direct/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\api\\\\raporty-direct\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_raporty_direct_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fraporty-direct%2Froute&page=%2Fapi%2Fraporty-direct%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fraporty-direct%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/raporty-direct/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/raporty-direct/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\nconst Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n\nasync function GET() {\n    let db = null;\n    try {\n        const dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"baza_danych.db\");\n        db = new Database(dbPath);\n        const raporty = db.prepare(`\n      SELECT \n        r.*,\n        p.kod_handlowy as produkt_kod,\n        p.nazwa as produkt_nazwa,\n        m.nazwa as maszyna_nazwa,\n        pr.imie as pracownik_imie,\n        pr.nazwisko as pracownik_nazwisko,\n        s.nazwa as surowiec_nazwa,\n        mat.nazwa as material_nazwa\n      FROM raporty_produkcyjne r\n      LEFT JOIN produkty p ON r.produkt_id = p.id\n      LEFT JOIN maszyny m ON r.maszyna_id = m.id\n      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id\n      LEFT JOIN surowce s ON r.surowiec_id = s.id\n      LEFT JOIN materialy mat ON r.material_id = mat.id\n      ORDER BY r.data_utworzenia DESC\n    `).all();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(raporty);\n    } catch (error) {\n        console.error(\"Błąd podczas pobierania raportów:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Błąd podczas pobierania raportów\"\n        }, {\n            status: 500\n        });\n    } finally{\n        if (db) {\n            db.close();\n        }\n    }\n}\nasync function POST(request) {\n    let db = null;\n    try {\n        const data = await request.json();\n        console.log(\"Received data for report creation:\", data);\n        const dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"baza_danych.db\");\n        db = new Database(dbPath);\n        // The frontend sends arrays for surowce and materialy.\n        // The database expects a single entry. We'll take the first one from each array.\n        const surowiec = data.surowce && data.surowce.length > 0 ? data.surowce[0] : {};\n        const material = data.materialy && data.materialy.length > 0 ? data.materialy[0] : {};\n        // Insert the report\n        const insertReport = db.prepare(`\n      INSERT INTO raporty_produkcyjne (\n        data_utworzenia,\n        produkt_id,\n        metraz_rolek,\n        ilosc_rolek,\n        surowiec_id,\n        zuzyty_surowiec,\n        odpad_surowiec,\n        material_id,\n        zuzyty_material,\n        odpad_material,\n        maszyna_id,\n        pracownik_id,\n        czas_pracy_maszyny,\n        czas_pracy_pracownika,\n        uwagi\n      ) VALUES (\n        datetime('now'),\n        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?\n      )\n    `);\n        // Validate required fields\n        if (!surowiec.surowiec_id || !material.material_id) {\n            throw new Error(\"Missing surowiec_id or material_id\");\n        }\n        const result = insertReport.run(data.produkt_id, data.metraz_rolek, data.ilosc_rolek, surowiec.surowiec_id, surowiec.zuzyty_surowiec || 0, surowiec.odpad_surowiec || 0, material.material_id, material.zuzyty_material || 0, material.odpad_material || 0, data.maszyna_id, data.pracownik_id, data.czas_pracy_maszyny, data.czas_pracy_pracownika, data.uwagi || null);\n        console.log(\"Report inserted with ID:\", result.lastInsertRowid);\n        // Fetch the created report with related data\n        const createdReport = db.prepare(`\n      SELECT\n        r.*,\n        p.kod_handlowy as produkt_kod,\n        p.nazwa as produkt_nazwa,\n        m.nazwa as maszyna_nazwa,\n        pr.imie as pracownik_imie,\n        pr.nazwisko as pracownik_nazwisko,\n        s.nazwa as surowiec_nazwa,\n        mat.nazwa as material_nazwa\n      FROM raporty_produkcyjne r\n      LEFT JOIN produkty p ON r.produkt_id = p.id\n      LEFT JOIN maszyny m ON r.maszyna_id = m.id\n      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id\n      LEFT JOIN surowce s ON r.surowiec_id = s.id\n      LEFT JOIN materialy mat ON r.material_id = mat.id\n      WHERE r.id = ?\n    `).get(result.lastInsertRowid);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(createdReport, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Błąd podczas tworzenia raportu:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Błąd podczas tworzenia raportu\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    } finally{\n        if (db) {\n            db.close();\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/raporty-direct/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fraporty-direct%2Froute&page=%2Fapi%2Fraporty-direct%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fraporty-direct%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();