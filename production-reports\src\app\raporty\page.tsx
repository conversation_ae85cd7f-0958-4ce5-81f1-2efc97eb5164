"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

interface RaportProdukcyjny {
  id: number;
  data_utworzenia: string;
  metraz_rolek: number;
  ilosc_rolek: number;
  zuzyty_surowiec: number;
  odpad_surowiec: number;
  zuzyty_material: number;
  odpad_material: number;
  czas_pracy_maszyny: number;
  czas_pracy_pracownika: number;
  uwagi: string | null;
  produkt: {
    kod_handlowy: string | null;
    nazwa: string | null;
    metraz: number;
    info: string | null;
  };
  maszyna: {
    nazwa: string;
  };
  pracownik: {
    numer: string;
    imie: string | null;
    nazwisko: string | null;
  };
  surowiec: {
    nazwa: string;
  };
  material: {
    nazwa: string;
  };
}

export default function RaportyPage() {
  const [raporty, setRaporty] = useState<RaportProdukcyjny[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [selectedShift, setSelectedShift] = useState("A");
  const [selectedWorker, setSelectedWorker] = useState("Paweł");

  useEffect(() => {
    fetchRaporty();
  }, [selectedDate]);

  const fetchRaporty = async () => {
    try {
      const response = await fetch(`/api/raporty-direct`);
      const data = await response.json();
      setRaporty(data);
    } catch (error) {
      console.error("Błąd podczas ładowania raportów:", error);
    } finally {
      setLoading(false);
    }
  };

  // Group reports by date for shift view
  const getShiftReports = () => {
    if (!raporty || !Array.isArray(raporty)) {
      return [];
    }
    return raporty.filter((raport) => {
      const reportDate = new Date(raport.data_utworzenia)
        .toISOString()
        .split("T")[0];
      return reportDate === selectedDate;
    });
  };

  const shiftReports = getShiftReports();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
        {/* Animated background particles */}
        <div className="absolute inset-0">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-blue-500 rounded-full opacity-20 animate-pulse-slow"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`,
              }}
            />
          ))}
        </div>

        <div className="glass-dark rounded-2xl p-12 border border-blue-500/20 shadow-2xl animate-fade-in-up relative z-10">
          <div className="flex flex-col items-center space-y-6">
            {/* Modern loading spinner */}
            <div className="relative">
              <div className="w-16 h-16 border-4 border-blue-500/20 rounded-full animate-spin"></div>
              <div className="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-blue-500 rounded-full animate-spin"></div>
              <div
                className="absolute top-2 left-2 w-12 h-12 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"
                style={{ animationDirection: "reverse" }}
              ></div>
            </div>

            {/* Loading text with shimmer effect */}
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-2 animate-shimmer">
                Ładowanie raportów produkcyjnych...
              </div>
              <div className="text-blue-400 animate-pulse">
                Przygotowywanie danych ze zmiany
              </div>
            </div>

            {/* Progress bar */}
            <div className="w-64 h-2 bg-gray-700 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-shimmer"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-10 left-10 w-32 h-32 bg-blue-500/5 rounded-full blur-xl animate-pulse-slow"></div>
        <div
          className="absolute top-40 right-20 w-24 h-24 bg-purple-500/5 rounded-full blur-xl animate-pulse-slow"
          style={{ animationDelay: "1s" }}
        ></div>
        <div
          className="absolute bottom-20 left-1/4 w-40 h-40 bg-cyan-500/5 rounded-full blur-xl animate-pulse-slow"
          style={{ animationDelay: "2s" }}
        ></div>
      </div>

      <div className="max-w-full mx-auto relative z-10">
        {/* Header Section with modern styling */}
        <div className="glass-dark rounded-t-2xl border border-gray-600/50 p-6 animate-slide-in-top shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-6">
              {/* Animated icon */}
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center floating">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
              </div>

              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                  RAPORT PRODUKCYJNY
                </h1>
                <div className="text-blue-400 font-medium">
                  {selectedDate} • Zmiana {selectedShift}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Company logo with glow effect */}
              <div className="relative">
                <div className="bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-xl font-bold text-lg shadow-lg animate-glow">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-sm font-black">AIB</span>
                    </div>
                    <span>ELASTOMERY</span>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-red-700 rounded-xl blur opacity-30 -z-10"></div>
              </div>
            </div>
          </div>

          {/* Control Panel with modern styling */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 animate-fade-in-up">
            <div className="group">
              <label className="block text-blue-300 text-sm font-medium mb-2 flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                Data:
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full glass border border-blue-500/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 group-hover:border-blue-400"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            <div className="group">
              <label className="block text-blue-300 text-sm font-medium mb-2 flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Zmiana:
              </label>
              <div className="relative">
                <select
                  value={selectedShift}
                  onChange={(e) => setSelectedShift(e.target.value)}
                  className="w-full glass border border-blue-500/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 group-hover:border-blue-400 appearance-none"
                >
                  <option value="A" className="bg-gray-800">
                    A
                  </option>
                  <option value="B" className="bg-gray-800">
                    B
                  </option>
                  <option value="C" className="bg-gray-800">
                    C
                  </option>
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <svg
                    className="w-5 h-5 text-blue-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            <div className="group">
              <label className="block text-blue-300 text-sm font-medium mb-2 flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                Kierownik zmiany:
              </label>
              <div className="relative">
                <select
                  value={selectedWorker}
                  onChange={(e) => setSelectedWorker(e.target.value)}
                  className="w-full glass border border-blue-500/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 group-hover:border-blue-400 appearance-none"
                >
                  <option value="Paweł" className="bg-gray-800">
                    Łaska Paweł
                  </option>
                  <option value="Jan" className="bg-gray-800">
                    Kowalski Jan
                  </option>
                  <option value="Anna" className="bg-gray-800">
                    Nowak Anna
                  </option>
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <svg
                    className="w-5 h-5 text-blue-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            <div className="group">
              <label className="block text-blue-300 text-sm font-medium mb-2 flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                  />
                </svg>
                Podpis:
              </label>
              <div className="relative">
                <div className="w-full glass border border-blue-500/30 rounded-xl px-4 py-3 text-center relative overflow-hidden group-hover:border-blue-400 transition-all duration-300">
                  <div className="text-blue-200 font-medium relative z-10">
                    {selectedWorker}
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area with Table and Sidebar */}
        <div className="flex glass-dark border-x border-b border-gray-600/50 rounded-b-2xl shadow-2xl animate-fade-in-up">
          {/* Main Table Area */}
          <div className="flex-1 overflow-x-auto">
            {shiftReports.length === 0 ? (
              <div className="text-center py-16 animate-fade-in-up">
                <div className="relative mb-8">
                  <div className="text-8xl mb-4 floating">📊</div>
                  <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-3xl"></div>
                </div>
                <p className="text-gray-300 text-2xl mb-8 font-light">
                  Brak raportów dla wybranej daty
                </p>
                <Link
                  href="/nowy-raport"
                  className="inline-block btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-2xl transition-all duration-300 hover:shadow-2xl font-semibold text-lg"
                >
                  <span className="flex items-center space-x-2">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                    <span>Dodaj raport</span>
                  </span>
                </Link>
              </div>
            ) : (
              <table className="w-full text-sm">
                <thead>
                  <tr className="bg-gray-700 border-b border-gray-600">
                    <th className="px-2 py-3 text-left text-white font-semibold border-r border-gray-600 w-12">
                      Lp
                    </th>
                    <th className="px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[120px]">
                      Kod handlowy
                    </th>
                    <th className="px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[200px]">
                      Nazwa wyrobu
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16">
                      Info
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Waga
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Metraż
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16">
                      Ilość
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Razem
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Suro
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24">
                      Siarką_1
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16">
                      Ilość
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Odpad
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Odpad
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Maszyna
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16">
                      ID_1
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold w-16">
                      Czas
                    </th>
                  </tr>
                </thead>
                <tbody className="glass">
                  {shiftReports.map((raport, index) => (
                    <tr
                      key={raport.id}
                      className={`table-row-hover border-b border-gray-700/50 transition-all duration-300 ${
                        index % 2 === 0 ? "bg-gray-800/50" : "bg-gray-750/50"
                      }`}
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-medium">
                        {index + 1}
                      </td>
                      <td className="px-3 py-2 text-white border-r border-gray-600 font-medium">
                        {raport.produkt_kod ||
                          raport.produkt?.kod_handlowy ||
                          "-"}
                      </td>
                      <td className="px-3 py-2 text-white border-r border-gray-600">
                        {raport.produkt_nazwa || raport.produkt?.nazwa || "-"}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                        {raport.produkt?.info || "-"}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {(raport.ilosc_rolek * 50).toFixed(0)}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.metraz_rolek.toFixed(0)}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.ilosc_rolek}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {(
                          raport.zuzyty_surowiec + raport.zuzyty_material
                        ).toFixed(0)}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.zuzyty_surowiec.toFixed(0)}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {new Date(raport.data_utworzenia).toLocaleTimeString(
                          "pl-PL",
                          {
                            hour: "2-digit",
                            minute: "2-digit",
                          }
                        )}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.zuzyty_material.toFixed(1)}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.odpad_surowiec.toFixed(0)}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.odpad_material.toFixed(0)}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                        <span className="bg-blue-600 text-white px-1 py-0.5 rounded text-xs">
                          {raport.maszyna_nazwa || raport.maszyna?.nazwa || "-"}
                        </span>
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.pracownik?.numer || "-"}
                      </td>
                      <td className="px-2 py-2 text-center text-white font-mono">
                        {raport.czas_pracy_maszyny.toFixed(1)}
                      </td>
                    </tr>
                  ))}

                  {/* Empty rows to fill the table like in the reference */}
                  {Array.from({
                    length: Math.max(0, 25 - shiftReports.length),
                  }).map((_, index) => (
                    <tr
                      key={`empty-${index}`}
                      className="border-b border-gray-700 bg-gray-800"
                    >
                      <td className="px-2 py-2 text-center text-gray-500 border-r border-gray-600">
                        {shiftReports.length + index + 1}
                      </td>
                      <td className="px-3 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-3 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2 border-r border-gray-600">
                        &nbsp;
                      </td>
                      <td className="px-2 py-2">&nbsp;</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>

          {/* Right Sidebar with modern styling */}
          <div className="w-72 glass-dark border-l border-gray-600/50 p-6 animate-slide-in-right">
            <div className="space-y-6">
              {/* Report List */}
              <div className="animate-fade-in-up">
                <div className="flex items-center mb-4">
                  <svg
                    className="w-5 h-5 text-blue-400 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  <h3 className="text-white font-bold text-lg bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                    ODŚWIEŹ LISTĘ
                  </h3>
                </div>
                <div className="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg animate-glow">
                    <div className="flex items-center justify-between">
                      <span>Raport 2025.06.04C</span>
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                  {["2025.06.03A", "2025.06.02B", "2025.06.01C"].map(
                    (date, index) => (
                      <div
                        key={date}
                        className="btn-modern glass border border-gray-600/30 text-white px-4 py-3 rounded-xl text-sm hover:border-blue-500/50 cursor-pointer transition-all duration-300"
                        style={{ animationDelay: `${index * 0.1}s` }}
                      >
                        <div className="flex items-center justify-between">
                          <span>Raport {date}</span>
                          <svg
                            className="w-4 h-4 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                            />
                          </svg>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div
                className="space-y-3 animate-fade-in-up"
                style={{ animationDelay: "0.2s" }}
              >
                <div className="flex items-center mb-3">
                  <svg
                    className="w-5 h-5 text-purple-400 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                  <h4 className="text-white font-semibold">AKCJE</h4>
                </div>

                {[
                  {
                    name: "ZAPIS RAPORTU",
                    icon: "💾",
                    color: "from-blue-600 to-indigo-600",
                  },
                  {
                    name: "ZAPAMIĘTAJ",
                    icon: "🧠",
                    color: "from-blue-600 to-indigo-600",
                  },
                  {
                    name: "ZAŁADUJ",
                    icon: "📂",
                    color: "from-blue-600 to-indigo-600",
                  },
                  {
                    name: "ZASOBY",
                    icon: "📊",
                    color: "from-blue-600 to-indigo-600",
                  },
                ].map((button, index) => (
                  <button
                    key={button.name}
                    className={`w-full btn-modern bg-gradient-to-r ${button.color} hover:shadow-xl text-white py-3 px-4 rounded-xl transition-all duration-300 font-medium text-sm flex items-center justify-center space-x-2`}
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <span className="text-lg">{button.icon}</span>
                    <span>{button.name}</span>
                  </button>
                ))}
              </div>

              {/* Date Range */}
              <div
                className="space-y-4 animate-fade-in-up"
                style={{ animationDelay: "0.4s" }}
              >
                <div className="flex items-center mb-3">
                  <svg
                    className="w-5 h-5 text-cyan-400 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  <h4 className="text-white font-semibold">ZAKRES DAT</h4>
                </div>

                <div className="space-y-3">
                  <div className="group">
                    <label className="block text-blue-300 text-sm font-medium mb-1">
                      Od:
                    </label>
                    <input
                      type="date"
                      defaultValue="2024-04-01"
                      className="w-full glass border border-blue-500/30 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300"
                    />
                  </div>
                  <div className="group">
                    <label className="block text-blue-300 text-sm font-medium mb-1">
                      Do:
                    </label>
                    <input
                      type="date"
                      defaultValue="2025-06-05"
                      className="w-full glass border border-blue-500/30 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300"
                    />
                  </div>

                  <div className="space-y-2 pt-2">
                    <button className="w-full btn-modern bg-gradient-to-r from-blue-600 to-indigo-600 hover:shadow-xl text-white py-3 px-4 rounded-xl transition-all duration-300 font-medium text-sm flex items-center justify-center space-x-2">
                      <span>📈</span>
                      <span>PODSUMOWANIE</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Navigation */}
              <div
                className="pt-6 border-t border-gray-600/50 animate-fade-in-up"
                style={{ animationDelay: "0.6s" }}
              >
                <div className="space-y-3">
                  <Link
                    href="/nowy-raport"
                    className="block w-full btn-modern bg-gradient-to-r from-blue-600 to-indigo-600 hover:shadow-2xl text-white py-4 px-4 rounded-xl transition-all duration-300 text-center font-semibold flex items-center justify-center space-x-2"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                    <span>Nowy Raport</span>
                  </Link>
                  <Link
                    href="/"
                    className="block w-full btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white py-3 px-4 rounded-xl transition-all duration-300 text-center font-medium flex items-center justify-center space-x-2"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 19l-7-7m0 0l7-7m-7 7h18"
                      />
                    </svg>
                    <span>Menu Główne</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
