// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Istniejąca tabela produktów
model Produkty {
  id              Int     @id @default(autoincrement())
  kod_handlowy    String?
  nazwa           String?
  metraz          Float
  surowiec        String?
  material        String?
  material_percent Float?  // Procent materiału
  material1       String? // Materiał 1
  material1_percent Float? // Procent materiału 1
  material2       String? // Materiał 2
  material2_percent Float? // Procent materiału 2
  material3       String? // Materiał 3
  material3_percent Float? // Procent materiału 3
  material4       String? // Materiał 4
  material4_percent Float? // Procent materiału 4
  material5       String? // Materiał 5
  material5_percent Float? // Procent materiału 5
  info            String?
  wydajnosc       Float?  // Wydajność produktu

  // Relacje do raportów produkcyjnych
  raporty RaportProdukcyjny[]

  @@map("produkty")
}

// Tabela maszyn
model Maszyna {
  id    Int    @id @default(autoincrement())
  nazwa String
  typ   String?
  info  String?

  // Relacje do raportów produkcyjnych
  raporty RaportProdukcyjny[]

  @@map("maszyny")
}

// Tabela pracowników
model Pracownik {
  id           Int    @id @default(autoincrement())
  numer        String @unique
  imie         String?
  nazwisko     String?
  stanowisko   String?

  // Relacje do raportów produkcyjnych
  raporty RaportProdukcyjny[]

  @@map("pracownicy")
}

// Tabela surowców
model Surowiec {
  id    Int    @id @default(autoincrement())
  nazwa String
  typ   String?
  info  String?

  // Relacje do raportów produkcyjnych
  raporty RaportProdukcyjny[]

  @@map("surowce")
}

// Tabela materiałów
model Material {
  id    Int    @id @default(autoincrement())
  nazwa String
  typ   String?
  info  String?

  // Relacje do raportów produkcyjnych
  raporty RaportProdukcyjny[]

  @@map("materialy")
}

// Główna tabela raportów produkcyjnych
model RaportProdukcyjny {
  id                    Int      @id @default(autoincrement())
  data_utworzenia       DateTime @default(now())

  // Relacje do produktu
  produkt_id            Int
  produkt               Produkty @relation(fields: [produkt_id], references: [id])

  // Dane produkcyjne
  metraz_rolek          Float
  ilosc_rolek           Int

  // Zużycie surowca
  surowiec_id           Int
  surowiec              Surowiec @relation(fields: [surowiec_id], references: [id])
  zuzyty_surowiec       Float
  odpad_surowiec        Float

  // Zużycie materiału
  material_id           Int
  material              Material @relation(fields: [material_id], references: [id])
  zuzyty_material       Float
  odpad_material        Float

  // Maszyna i pracownik
  maszyna_id            Int
  maszyna               Maszyna @relation(fields: [maszyna_id], references: [id])
  pracownik_id          Int
  pracownik             Pracownik @relation(fields: [pracownik_id], references: [id])

  // Czasy pracy
  czas_pracy_maszyny    Float // w godzinach
  czas_pracy_pracownika Float // w godzinach

  // Dodatkowe informacje
  uwagi                 String?

  @@map("raporty_produkcyjne")
}
